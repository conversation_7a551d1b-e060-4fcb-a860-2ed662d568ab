#!/usr/bin/env python3
import os
import time
import sqlite3
from pyboy import PyBoy

class PyBoyController:
    def __init__(self, rom_path='redc.gb', config=None):
        self.rom_path = rom_path
        self.pyboy = None
        self.last_move_id = 0  # Track last processed move from database
        self.config = config or {
            'pyboy_tick_sleep': 0.001,
            'pyboy_state_save_ticks': 5000
        }

        self.get_last_move()
        
    def initialize_pyboy(self):
        """Initialize PyBoy with the ROM"""
        try:
            self.pyboy = PyBoy(self.rom_path, cgb=True)
            print(f"PyBoy initialized with ROM: {self.rom_path}")
            if os.path.exists('state.state'):
                with open("state.state", "rb") as r:
                    self.pyboy.load_state(r)

            print(f"Loaded state")
            return True
        except Exception as e:
            print(f"Error initializing PyBoy: {e}")
            return False
    
    
    def get_last_move(self):
        """Check database for new moves since last processed"""
        try:
            conn = sqlite3.connect('pump_pokemon.db')
            cursor = conn.execute('SELECT max(id) from game_outcomes')
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                result = result[0]
                print(f"Fetched last id {result}")
                self.last_move_id = result
            
            else:
                self.last_move_id = 0

        except Exception as e:
            print(f"Error checking database: {e}")
              
    def check_database_for_new_move(self):
        """Check database for new moves since last processed"""
        try:
            conn = sqlite3.connect('pump_pokemon.db')
            cursor = conn.execute('''
                SELECT id, winning_move FROM game_outcomes 
                WHERE id > ? 
                ORDER BY id ASC 
                LIMIT 1
            ''', (self.last_move_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                move_id, move = result
                self.last_move_id = move_id
                print(f"New move from database: {move.lower()} (ID: {move_id})")
                return move.lower()
            
        except Exception as e:
            print(f"Error checking database: {e}")
        
        return None
    
    def execute_move(self, move):
        """Execute a move on the PyBoy instance"""
        if not self.pyboy:
            print("PyBoy not initialized")
            return
        
        # Map moves to PyBoy button methods
        button_map = {
            'a': 'a',
            'b': 'b', 
            'start': 'start',
            'select': 'select',
            'up': 'up',
            'down': 'down',
            'left': 'left',
            'right': 'right'
        }
        
        if move in button_map:
            try:
                # Press the button
                self.pyboy.button(button_map[move])
                print(f"Executed move: {move.upper()}")
            except Exception as e:
                print(f"Error executing move {move}: {e}")
        else:
            print(f"Unknown move: {move}")
    
    def run(self):
        """Main game loop"""
        if not self.initialize_pyboy():
            return
        
        ticks = 0
        print("Starting PyBoy game loop...")
        print("Checking for moves via lock file and database...")
        
        try:
            while self.pyboy.tick():
                ticks += 1
                # Check database for new moves
                move_from_db = self.check_database_for_new_move()
                if move_from_db:
                    self.execute_move(move_from_db)
                
                # Configurable sleep to prevent excessive CPU usage
                time.sleep(self.config['pyboy_tick_sleep'])

                if ticks % self.config['pyboy_state_save_ticks'] == 0:
                    with open("state.state", "wb") as f:
                        self.pyboy.save_state(f)
                    print("saved state")
                    ticks = 1
                    
        except KeyboardInterrupt:
            print("\nShutting down PyBoy...")
            with open("state.state", "wb") as f:
                self.pyboy.save_state(f)
            print("saved state")

        except Exception as e:
            print(f"Error in game loop: {e}")
        finally:
            if self.pyboy:
                self.pyboy.stop()


if __name__ == "__main__":
    # Import CONFIG from main.py if available
    try:
        import sys
        sys.path.append('.')
        from main import CONFIG
        controller = PyBoyController(config=CONFIG)
    except ImportError:
        controller = PyBoyController()
    controller.run()